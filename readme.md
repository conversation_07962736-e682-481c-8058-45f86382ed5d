我有一个需求

<purpose>
请帮我写一个基于golang使用vmess vless的代理服务。
我有一个订阅 url：https://blue-cell-1adc.teleapp.top/fc1561f8-7d8c-4f81-bc9c-66cfb3aa7a5f?clash 返回的是yaml配置。
请帮我加载这个配置，应用配置中的vmess vless代理。
然后对外提供一个 API服务：
1. 显示当前可用代理 
2. 对外提供代理服务（可选代理或随机）
3. 我希望直接使用v2ray相关的core的包来实现client，所以理论上是不需要再下载相关二进制文件的
4. 现有实现代码：@main.go，你可以直接修改，甚至重写代码实现。
</purpose>

现有实现代码：@main.go

但是目前运行报错：
<terminal-output>
(base) conan@cosinworkshop:~/conan-work/goProxy$ go run main.go 
2025/07/19 09:42:19 [main] 启动中...
2025/07/19 09:42:19 [fetchAndLoadConfig] 开始加载配置...
2025/07/19 09:42:19 [fetchAndLoadConfig] 正在下载远程配置...
2025/07/19 09:42:25 已下载并缓存配置文件: config_cache.yaml
2025/07/19 09:42:25 [fetchAndLoadConfig] 解析YAML配置...
2025/07/19 09:42:25 [fetchAndLoadConfig] 解析完成，已加载 21 个代理
2025/07/19 09:42:25 [main] 启动代理服务（随机模式）...
2025/07/19 09:42:25 [restartProxyServer] 正在重启代理服务...
2025/07/19 09:42:25 [restartProxyServer] 构建inbounds配置...
2025/07/19 09:42:25 [restartProxyServer] 构建outbounds配置...
2025/07/19 09:42:25 [restartProxyServer] 追加freedom直连出口...
2025/07/19 09:42:25 [restartProxyServer] 构建总配置...
2025/07/19 09:42:25 [restartProxyServer] 最终生成的Xray配置如下：
==== Xray Config ====
2025/07/19 09:42:25 [restartProxyServer] 已写入coreConfig.json
2025/07/19 09:42:25 [restartProxyServer] 启动Xray实例...
2025/07/19 09:42:25 [restartProxyServer] 加载配置失败: core: Unable to load config
2025/07/19 09:42:25 [main] 启动代理服务失败: core: Unable to load config
exit status 1
</terminal-output>

请帮我解决这个问题，你可以调用工具，你也可以联网查询文档。