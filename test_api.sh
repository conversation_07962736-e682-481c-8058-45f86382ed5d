#!/bin/bash

# GoProxy API 测试脚本
API_BASE="http://localhost:8080"

echo "🚀 GoProxy API 测试脚本"
echo "========================"

# 测试服务状态
echo "1. 测试服务状态..."
curl -s "$API_BASE/status" | jq '.' 2>/dev/null || curl -s "$API_BASE/status"
echo -e "\n"

# 获取代理列表
echo "2. 获取代理列表..."
curl -s "$API_BASE/proxies" | jq '.' 2>/dev/null || curl -s "$API_BASE/proxies"
echo -e "\n"

# 选择随机代理
echo "3. 选择随机代理..."
curl -s -X POST -H "Content-Type: application/json" -d '{"name":"random"}' "$API_BASE/select"
echo -e "\n"

# 再次检查状态
echo "4. 检查切换后状态..."
curl -s "$API_BASE/status" | jq '.' 2>/dev/null || curl -s "$API_BASE/status"
echo -e "\n"

# 刷新配置
echo "5. 刷新配置..."
curl -s -X POST "$API_BASE/refresh" | jq '.' 2>/dev/null || curl -s -X POST "$API_BASE/refresh"
echo -e "\n"

echo "✅ 测试完成！"
echo "💡 提示: 在浏览器中访问 http://localhost:8080 查看Web界面"
echo "🔧 SOCKS5代理地址: 127.0.0.1:1080"